"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import * as Diff from 'diff';
import {
    ArrowLeft,
    ArrowLeftRight,
    CheckCircle2,
    Copy,
    Download,
    FileText,
    RotateCcw
} from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

interface DifferenceCheckerState {
  leftText: string;
  rightText: string;
  diffResult: Diff.Change[] | null;
  isComparing: boolean;
  comparisonMode: 'lines' | 'words' | 'chars' | 'json';
  showUnchanged: boolean;
  viewMode: 'unified' | 'split';
  ignoreWhitespace: boolean;
  ignoreCase: boolean;
}

export default function DifferenceChecker() {
  const [state, setState] = useState<DifferenceCheckerState>({
    leftText: '',
    rightText: '',
    diffResult: null,
    isComparing: false,
    comparisonMode: 'lines',
    showUnchanged: true,
    viewMode: 'unified',
    ignoreWhitespace: false,
    ignoreCase: false,
  });

  // Debounced comparison function
  const performComparison = useCallback(() => {
    if (!state.leftText && !state.rightText) {
      setState(prev => ({ ...prev, diffResult: null }));
      return;
    }

    setState(prev => ({ ...prev, isComparing: true }));

    try {
      let leftText = state.leftText;
      let rightText = state.rightText;

      // Apply preprocessing options
      if (state.ignoreCase) {
        leftText = leftText.toLowerCase();
        rightText = rightText.toLowerCase();
      }

      if (state.ignoreWhitespace) {
        leftText = leftText.replace(/\s+/g, ' ').trim();
        rightText = rightText.replace(/\s+/g, ' ').trim();
      }

      let diffResult: Diff.Change[];

      switch (state.comparisonMode) {
        case 'lines':
          diffResult = Diff.diffLines(leftText, rightText, {
            ignoreWhitespace: state.ignoreWhitespace,
          });
          break;
        case 'words':
          diffResult = Diff.diffWords(leftText, rightText, {
            ignoreCase: state.ignoreCase,
          });
          break;
        case 'chars':
          diffResult = Diff.diffChars(leftText, rightText);
          break;
        case 'json':
          try {
            const leftJson = JSON.parse(leftText);
            const rightJson = JSON.parse(rightText);
            const leftFormatted = JSON.stringify(leftJson, null, 2);
            const rightFormatted = JSON.stringify(rightJson, null, 2);
            diffResult = Diff.diffLines(leftFormatted, rightFormatted);
          } catch (jsonError) {
            toast.error('Invalid JSON format');
            diffResult = Diff.diffLines(leftText, rightText);
          }
          break;
        default:
          diffResult = Diff.diffLines(leftText, rightText);
      }

      setState(prev => ({
        ...prev,
        diffResult,
        isComparing: false
      }));
    } catch (error) {
      console.error('Diff comparison error:', error);
      toast.error('Error comparing texts');
      setState(prev => ({ ...prev, isComparing: false }));
    }
  }, [state.leftText, state.rightText, state.comparisonMode, state.ignoreCase, state.ignoreWhitespace]);

  // Auto-compare with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      performComparison();
    }, 500);

    return () => clearTimeout(timer);
  }, [performComparison]);

   const handleSwapTexts = () => {
    setState(prev => ({
      ...prev,
      leftText: prev.rightText,
      rightText: prev.leftText,
    }));
  };

  const handleClearAll = () => {
    setState(prev => ({
      ...prev,
      leftText: '',
      rightText: '',
      diffResult: null,
    }));
  };

  const handleCopyResult = async () => {
    if (!state.diffResult) return;
    
    const resultText = state.diffResult
      .map(part => part.value)
      .join('');
    
    try {
      await navigator.clipboard.writeText(resultText);
      toast.success('Diff result copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'Enter':
            event.preventDefault();
            performComparison();
            break;
          case 'k':
            event.preventDefault();
            handleClearAll();
            break;
          case 'c':
            if (event.shiftKey) {
              event.preventDefault();
              handleCopyResult();
            }
            break;
          case 's':
            if (event.shiftKey) {
              event.preventDefault();
              handleSwapTexts();
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [performComparison, handleClearAll, handleCopyResult, handleSwapTexts]);

 

  const handleExportDiff = () => {
    if (!state.diffResult) return;

    const diffText = state.diffResult
      .map(part => {
        if (part.added) return `+ ${part.value}`;
        if (part.removed) return `- ${part.value}`;
        return `  ${part.value}`;
      })
      .join('');

    const blob = new Blob([diffText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'diff-result.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Diff exported successfully');
  };

  const getDiffStats = () => {
    if (!state.diffResult) return { additions: 0, deletions: 0, unchanged: 0 };
    
    return state.diffResult.reduce(
      (stats, part) => {
        if (part.added) stats.additions++;
        else if (part.removed) stats.deletions++;
        else stats.unchanged++;
        return stats;
      },
      { additions: 0, deletions: 0, unchanged: 0 }
    );
  };

  const stats = getDiffStats();

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="mx-auto max-w-7xl space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Tools
              </Link>
            </Button>
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary/10 text-primary">
                <FileText className="h-5 w-5" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">Difference Checker</h1>
                <p className="text-sm text-muted-foreground">
                  Compare two text inputs and highlight differences
                </p>
              </div>
            </div>
          </div>
          
          {/* Stats */}
          {state.diffResult && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-800">
                +{stats.additions} additions
              </Badge>
              <Badge variant="secondary" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950/20 dark:text-red-400 dark:border-red-800">
                -{stats.deletions} deletions
              </Badge>
              <Badge variant="secondary">
                {stats.unchanged} unchanged
              </Badge>
            </div>
          )}
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              {/* First Row - Comparison Settings */}
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="comparison-mode" className="text-sm font-medium">
                    Mode:
                  </Label>
                  <select
                    id="comparison-mode"
                    value={state.comparisonMode}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      comparisonMode: e.target.value as 'lines' | 'words' | 'chars' | 'json'
                    }))}
                    className="px-3 py-1 text-sm border border-input rounded-md bg-background"
                  >
                    <option value="lines">Line by Line</option>
                    <option value="words">Word by Word</option>
                    <option value="chars">Character by Character</option>
                    <option value="json">JSON Structure</option>
                  </select>
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="view-mode" className="text-sm font-medium">
                    View:
                  </Label>
                  <select
                    id="view-mode"
                    value={state.viewMode}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      viewMode: e.target.value as 'unified' | 'split'
                    }))}
                    className="px-3 py-1 text-sm border border-input rounded-md bg-background"
                  >
                    <option value="unified">Unified</option>
                    <option value="split">Side by Side</option>
                  </select>
                </div>

                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2 text-sm">
                    <input
                      type="checkbox"
                      checked={state.ignoreCase}
                      onChange={(e) => setState(prev => ({ ...prev, ignoreCase: e.target.checked }))}
                      className="rounded border-input"
                    />
                    Ignore Case
                  </label>
                  <label className="flex items-center gap-2 text-sm">
                    <input
                      type="checkbox"
                      checked={state.ignoreWhitespace}
                      onChange={(e) => setState(prev => ({ ...prev, ignoreWhitespace: e.target.checked }))}
                      className="rounded border-input"
                    />
                    Ignore Whitespace
                  </label>
                  <label className="flex items-center gap-2 text-sm">
                    <input
                      type="checkbox"
                      checked={state.showUnchanged}
                      onChange={(e) => setState(prev => ({ ...prev, showUnchanged: e.target.checked }))}
                      className="rounded border-input"
                    />
                    Show Unchanged
                  </label>
                </div>
              </div>

              {/* Second Row - Action Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={performComparison}
                    disabled={state.isComparing}
                  >
                    {state.isComparing ? (
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <CheckCircle2 className="h-4 w-4" />
                    )}
                    Compare Now
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSwapTexts}
                    disabled={!state.leftText && !state.rightText}
                  >
                    <ArrowLeftRight className="h-4 w-4" />
                    Swap
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                    disabled={!state.leftText && !state.rightText}
                  >
                    <RotateCcw className="h-4 w-4" />
                    Clear All
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyResult}
                    disabled={!state.diffResult}
                  >
                    <Copy className="h-4 w-4" />
                    Copy Result
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportDiff}
                    disabled={!state.diffResult}
                  >
                    <Download className="h-4 w-4" />
                    Export
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Input Areas */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Input */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                Original Text
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Paste your original text here..."
                value={state.leftText}
                onChange={(e) => setState(prev => ({ ...prev, leftText: e.target.value }))}
                className="min-h-[300px] font-mono text-sm resize-none"
                aria-label="Original text input"
              />
            </CardContent>
          </Card>

          {/* Right Input */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <span className="w-3 h-3 bg-orange-500 rounded-full"></span>
                Modified Text
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Paste your modified text here..."
                value={state.rightText}
                onChange={(e) => setState(prev => ({ ...prev, rightText: e.target.value }))}
                className="min-h-[300px] font-mono text-sm resize-none"
                aria-label="Modified text input"
              />
            </CardContent>
          </Card>
        </div>

        {/* Diff Result Display */}
        {state.diffResult && (
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                Comparison Result
                {state.isComparing && (
                  <div className="ml-2 w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {state.viewMode === 'unified' ? (
                <ScrollArea className="h-[400px] w-full rounded-md border p-4">
                  <div className="space-y-1 font-mono text-sm">
                    {state.diffResult.map((part, index) => {
                      if (!state.showUnchanged && !part.added && !part.removed) {
                        return null;
                      }

                      let bgColor = '';
                      let textColor = '';
                      let prefix = '';

                      if (part.added) {
                        bgColor = 'bg-green-100 dark:bg-green-950/30';
                        textColor = 'text-green-800 dark:text-green-200';
                        prefix = '+ ';
                      } else if (part.removed) {
                        bgColor = 'bg-red-100 dark:bg-red-950/30';
                        textColor = 'text-red-800 dark:text-red-200';
                        prefix = '- ';
                      } else {
                        bgColor = 'bg-gray-50 dark:bg-gray-900/30';
                        textColor = 'text-gray-700 dark:text-gray-300';
                        prefix = '  ';
                      }

                      return (
                        <div
                          key={index}
                          className={`px-2 py-1 rounded ${bgColor} ${textColor} whitespace-pre-wrap break-words`}
                        >
                          <span className="select-none opacity-60">{prefix}</span>
                          {part.value}
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              ) : (
                <div className="grid grid-cols-2 gap-4 h-[400px]">
                  {/* Left Side - Removed/Original */}
                  <div className="border rounded-md">
                    <div className="bg-red-50 dark:bg-red-950/20 px-3 py-2 border-b">
                      <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                        Original (Removed)
                      </h4>
                    </div>
                    <ScrollArea className="h-[350px] p-4">
                      <div className="space-y-1 font-mono text-sm">
                        {state.diffResult
                          .filter(part => part.removed || (!part.added && !part.removed))
                          .map((part, index) => (
                            <div
                              key={`left-${index}`}
                              className={`px-2 py-1 rounded whitespace-pre-wrap break-words ${
                                part.removed
                                  ? 'bg-red-100 dark:bg-red-950/30 text-red-800 dark:text-red-200'
                                  : 'bg-gray-50 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
                              }`}
                            >
                              {part.value}
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </div>

                  {/* Right Side - Added/Modified */}
                  <div className="border rounded-md">
                    <div className="bg-green-50 dark:bg-green-950/20 px-3 py-2 border-b">
                      <h4 className="text-sm font-medium text-green-800 dark:text-green-200">
                        Modified (Added)
                      </h4>
                    </div>
                    <ScrollArea className="h-[350px] p-4">
                      <div className="space-y-1 font-mono text-sm">
                        {state.diffResult
                          .filter(part => part.added || (!part.added && !part.removed))
                          .map((part, index) => (
                            <div
                              key={`right-${index}`}
                              className={`px-2 py-1 rounded whitespace-pre-wrap break-words ${
                                part.added
                                  ? 'bg-green-100 dark:bg-green-950/30 text-green-800 dark:text-green-200'
                                  : 'bg-gray-50 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
                              }`}
                            >
                              {part.value}
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {!state.diffResult && !state.isComparing && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Ready to Compare
              </h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-6">
                Enter text in both input areas above to see the differences highlighted below.
                The comparison will update automatically as you type.
              </p>

              {/* Keyboard Shortcuts */}
              <div className="max-w-lg mx-auto">
                <h4 className="text-sm font-medium text-foreground mb-3">Keyboard Shortcuts</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Compare Now:</span>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+Enter</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Clear All:</span>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+K</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Copy Result:</span>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+Shift+C</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Swap Texts:</span>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+Shift+S</kbd>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
