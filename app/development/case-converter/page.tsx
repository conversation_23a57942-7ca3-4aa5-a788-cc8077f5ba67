"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    ArrowLeft,
    CheckCircle2,
    Copy,
    RotateCcw,
    Type
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface CaseFormat {
  name: string;
  description: string;
  convert: (text: string) => string;
}

interface CaseConverterState {
  inputText: string;
  formats: CaseFormat[];
}

export default function CaseConverter() {
  const [state, setState] = useState<CaseConverterState>({
    inputText: '',
    formats: []
  });

  // Initialize case formats
  useEffect(() => {
    const formats: CaseFormat[] = [
      {
        name: 'lowercase',
        description: 'all lowercase letters',
        convert: (text: string) => text.toLowerCase()
      },
      {
        name: 'UPPERCASE',
        description: 'all uppercase letters',
        convert: (text: string) => text.toUpperCase()
      },
      {
        name: 'camelCase',
        description: 'first word lowercase, then capitalized words',
        convert: (text: string) => {
          return text
            // Split on word boundaries, including camelCase
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            .replace(/[^a-zA-Z0-9]/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map((word, index) => {
              if (index === 0) {
                return word.toLowerCase();
              }
              return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');
        }
      },
      {
        name: 'PascalCase',
        description: 'each word capitalized',
        convert: (text: string) => {
          return text
            // Split on word boundaries, including camelCase
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            .replace(/[^a-zA-Z0-9]/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join('');
        }
      },
      {
        name: 'Capital Case',
        description: 'same as Pascal but with spaces',
        convert: (text: string) => {
          return text
            // Split on word boundaries, including camelCase
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            .replace(/[^a-zA-Z0-9]/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
        }
      },
      {
        name: 'Title Case',
        description: 'Each word capitalized with spaces',
        convert: (text: string) => {
          return text
            // Split on word boundaries, including camelCase
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            .replace(/[^a-zA-Z0-9]/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
        }
      },
      {
        name: 'Sentence case',
        description: 'First word capitalized, rest lowercase',
        convert: (text: string) => {
          return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
        }
      },
      {
        name: 'snake_case',
        description: 'words separated by underscores',
        convert: (text: string) => {
          return text
            .replace(/\W+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.toLowerCase())
            .join('_');
        }
      },
      {
        name: 'CONSTANT_CASE',
        description: 'all caps with underscores',
        convert: (text: string) => {
          return text
            .replace(/\W+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.toUpperCase())
            .join('_');
        }
      },
      {
        name: 'param-case',
        description: 'words separated by hyphens',
        convert: (text: string) => {
          return text
            .replace(/\W+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.toLowerCase())
            .join('-');
        }
      },
      {
        name: 'Header-Case',
        description: 'Each-Word-Capitalized and separated by hyphens',
        convert: (text: string) => {
          return text
            .replace(/\W+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join('-');
        }
      },
      {
        name: 'dot.case',
        description: 'words separated by dots',
        convert: (text: string) => {
          return text
            .replace(/\W+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.toLowerCase())
            .join('.');
        }
      },
      {
        name: 'path/case',
        description: 'words separated by slashes',
        convert: (text: string) => {
          return text
            .replace(/\W+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word => word.toLowerCase())
            .join('/');
        }
      },
      {
        name: 'sWAP cASE',
        description: 'switch lowercase ↔ uppercase',
        convert: (text: string) => {
          return text
            .split('')
            .map(char => {
              if (char === char.toLowerCase()) {
                return char.toUpperCase();
              } else {
                return char.toLowerCase();
              }
            })
            .join('');
        }
      }
    ];

    setState(prev => ({ ...prev, formats }));
  }, []);

  const handleInputChange = (value: string) => {
    setState(prev => ({ ...prev, inputText: value }));
  };

  const handleClearInput = () => {
    setState(prev => ({ ...prev, inputText: '' }));
  };

  const handleCopyToClipboard = async (text: string, formatName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${formatName} copied to clipboard`);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleCopyAll = async () => {
    if (!state.inputText.trim()) return;

    const allFormats = state.formats
      .map(format => `${format.name}: ${format.convert(state.inputText)}`)
      .join('\n');

    try {
      await navigator.clipboard.writeText(allFormats);
      toast.success('All formats copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
            event.preventDefault();
            handleClearInput();
            break;
          case 'a':
            if (event.shiftKey) {
              event.preventDefault();
              handleCopyAll();
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleCopyAll]);

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="mx-auto max-w-7xl space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Tools
              </Link>
            </Button>
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary/10 text-primary">
                <Type className="h-5 w-5" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">Case Converter</h1>
                <p className="text-sm text-muted-foreground">
                  Convert text into various naming conventions and case formats
                </p>
              </div>
            </div>
          </div>
          
          {/* Stats */}
          {state.inputText && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {state.inputText.length} characters
              </Badge>
              <Badge variant="secondary">
                {state.inputText.split(/\s+/).filter(word => word.length > 0).length} words
              </Badge>
            </div>
          )}
        </div>

        {/* Input Section */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                Input Text
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearInput}
                  disabled={!state.inputText}
                >
                  <RotateCcw className="h-4 w-4" />
                  Clear
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyAll}
                  disabled={!state.inputText.trim()}
                >
                  <Copy className="h-4 w-4" />
                  Copy All
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="text-input" className="sr-only">
                Text to convert
              </Label>
              <Textarea
                id="text-input"
                placeholder="Enter or paste your text here... e.g., 'hello world', 'myVariableName', 'convert THIS'"
                value={state.inputText}
                onChange={(e) => handleInputChange(e.target.value)}
                className="min-h-[120px] font-mono text-sm resize-none"
                aria-label="Text input for case conversion"
              />
              <div className="text-xs text-muted-foreground">
                Tip: Enter any text to see it converted into different case formats below
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Output Section */}
        {state.inputText.trim() ? (
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                Converted Formats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {state.formats.map((format, index) => {
                  const convertedText = format.convert(state.inputText);
                  return (
                    <div
                      key={index}
                      className="group relative border rounded-lg p-4 hover:border-primary/30 transition-colors"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-medium text-sm text-foreground">
                            {format.name}
                          </h3>
                          <p className="text-xs text-muted-foreground">
                            {format.description}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                          onClick={() => handleCopyToClipboard(convertedText, format.name)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="bg-muted/50 rounded-md p-3 mt-2">
                        <code className="text-sm font-mono break-all text-foreground">
                          {convertedText}
                        </code>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ) : (
          /* Empty State */
          <Card>
            <CardContent className="p-12 text-center">
              <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <Type className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Ready to Convert
              </h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-6">
                Enter any text in the input field above to see it converted into various case formats.
                Perfect for variable names, file names, and different coding conventions.
              </p>

              {/* Example formats preview */}
              <div className="max-w-lg mx-auto mb-6">
                <h4 className="text-sm font-medium text-foreground mb-3">Available Formats</h4>
                <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                  <div>camelCase</div>
                  <div>PascalCase</div>
                  <div>snake_case</div>
                  <div>param-case</div>
                  <div>CONSTANT_CASE</div>
                  <div>dot.case</div>
                  <div>Title Case</div>
                  <div>And more...</div>
                </div>
              </div>

              {/* Keyboard Shortcuts */}
              <div className="max-w-lg mx-auto">
                <h4 className="text-sm font-medium text-foreground mb-3">Keyboard Shortcuts</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Clear Input:</span>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+K</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Copy All:</span>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+Shift+A</kbd>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
