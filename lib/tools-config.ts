import { Calculator, FileArchive, FileAudio, FileCode, FileImage, FileJson, FileSpreadsheet, FileText, FileVideo, Receipt } from 'lucide-react';

export interface Tool {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  enabled: boolean;
  comingSoon?: boolean;
  category: string;
}

export interface ToolCategory {
  id: string;
  name: string;
  description: string;
  tools: Tool[];
}

export const toolCategories: ToolCategory[] = [
  {
    id: 'business',
    name: 'Business Tools',
    description: 'Essential tools for business operations and documentation',
    tools: [
      {
        id: 'invoice-generator',
        name: 'Invoice Generator',
        description: 'Create professional invoices with customizable templates and PDF export.',
        icon: Receipt,
        href: '/business/invoice-generator',
        enabled: true,
        category: 'business',
      },
      {
        id: 'quote-generator',
        name: 'Quote Generator',
        description: 'Generate detailed quotes with itemized lists and PDF export.',
        icon: FileText,
        href: '/business/quote-generator',
        enabled: true,
        category: 'business',
      },
    ],
  },
  {
    id: 'development',
    name: 'Development Tools',
    description: 'Tools for developers and programmers',
    tools: [
      {
        id: 'code-editor',
        name: 'Code Editor',
        description: 'Write and test code in multiple languages',
        icon: FileCode,
        href: '/development/code-editor',
        enabled: false,
        category: 'development',
      },
      {
        id: 'json-formatter',
        name: 'JSON Formatter',
        description: 'Format and validate JSON data',
        icon: FileJson,
        href: '/development/json-formatter',
        enabled: true,
        category: 'development',
      },
      {
        id: 'difference-checker',
        name: 'Difference Checker',
        description: 'Compare two text inputs and highlight differences',
        icon: FileText,
        href: '/development/difference-checker',
        enabled: true,
        category: 'development',
      },
      {
        id: 'case-converter',
        name: 'Case Converter',
        description: 'Convert text to different case formats',
        icon: FileText,
        href: '/development/case-converter',
        enabled: true,
        category: 'development',
      },
    ],
  },
  {
    id: 'media',
    name: 'Media Tools',
    description: 'Tools for working with images, audio, and video',
    tools: [
      {
        id: 'image-editor',
        name: 'Image Editor',
        description: 'Edit and optimize your images',
        icon: FileImage,
        href: '/media/image-editor',
        enabled: true,
        category: 'media',
      },
      {
        id: 'audio-converter',
        name: 'Audio Converter',
        description: 'Convert audio files between different formats',
        icon: FileAudio,
        href: '/media/audio-converter',
        enabled: false,
        category: 'media',
      },
      {
        id: 'video-converter',
        name: 'Video Converter',
        description: 'Convert video files between different formats',
        icon: FileVideo,
        href: '/media/video-converter',
        enabled: false,
        category: 'media',
      },
    ],
  },
  {
    id: 'utilities',
    name: 'Utility Tools',
    description: 'General purpose tools for everyday use',
    tools: [
      {
        id: 'calculator',
        name: 'Calculator',
        description: 'Perform complex calculations with ease',
        icon: Calculator,
        href: '/development/calculator',
        enabled: true,
        category: 'utilities',
      },
      {
        id: 'spreadsheet',
        name: 'Spreadsheet',
        description: 'Edit and manage spreadsheets online',
        icon: FileSpreadsheet,
        href: '/utilities/spreadsheet',
        enabled: false,
        category: 'utilities',
      },
      {
        id: 'file-compressor',
        name: 'File Compressor',
        description: 'Compress files and folders',
        icon: FileArchive,
        href: '/utilities/file-compressor',
        enabled: false,
        category: 'utilities',
      },
    ],
  },
];

// Flatten tools array for backward compatibility
export const tools: Tool[] = toolCategories.flatMap(category => category.tools); 