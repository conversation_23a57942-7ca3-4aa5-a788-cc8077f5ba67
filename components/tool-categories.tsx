"use client";

import { ToolCard } from "@/components/tool-card";
import { ToolCardSkeletonGrid } from "@/components/tool-card-skeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toolCategories } from "@/lib/tools-config";
import {
  SearchIcon
} from "lucide-react";
import { useMemo, useState } from "react";

type ViewMode = "grid" | "list";
type SortBy = "name" | "category" | "status";
type FilterBy = "all" | "enabled" | "coming-soon";

export function ToolCategories() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [sortBy, setSortBy] = useState<SortBy>("category");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [filterBy, setFilterBy] = useState<FilterBy>("all");
  const [isLoading, setIsLoading] = useState(false);

  // Get all tools flattened
  const allTools = useMemo(() => 
    toolCategories.flatMap(category => 
      category.tools.map(tool => ({ ...tool, categoryName: category.name }))
    ), []
  );

  // Filter and sort tools
  const filteredTools = useMemo(() => {
    let filtered = allTools;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.categoryName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (activeCategory !== "all") {
      filtered = filtered.filter(tool => tool.category === activeCategory);
    }

    // Filter by status
    if (filterBy === "enabled") {
      filtered = filtered.filter(tool => tool.enabled);
    } else if (filterBy === "coming-soon") {
      filtered = filtered.filter(tool => !tool.enabled);
    }

    // Sort tools
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "category":
          comparison = a.categoryName.localeCompare(b.categoryName);
          break;
        case "status":
          comparison = (a.enabled ? 1 : 0) - (b.enabled ? 1 : 0);
          break;
      }
      
      return sortOrder === "asc" ? comparison : -comparison;
    });

    return filtered;
  }, [allTools, searchQuery, activeCategory, filterBy, sortBy, sortOrder]);

  const handleSearch = (query: string) => {
    setIsLoading(true);
    setSearchQuery(query);
    // Simulate loading delay
    setTimeout(() => setIsLoading(false), 300);
  };

  const toggleSort = () => {
    setSortOrder(prev => prev === "asc" ? "desc" : "asc");
  };

  const enabledCount = allTools.filter(tool => tool.enabled).length;
  const totalCount = allTools.length;

  return (
    <section id="tools" className="w-full py-16 md:py-20 lg:py-24 bg-muted/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 text-foreground">
            Professional Tools
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            Comprehensive suite of business-grade tools designed for professionals and enterprises
          </p>
          <div className="flex items-center justify-center gap-6 text-sm">
            <div className="flex items-center gap-2 px-3 py-1.5 bg-green-50 dark:bg-green-950/20 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-green-700 dark:text-green-400 font-medium">{enabledCount} Available</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-amber-50 dark:bg-amber-950/20 rounded-full">
              <div className="w-2 h-2 bg-amber-500 rounded-full" />
              <span className="text-amber-700 dark:text-amber-400 font-medium">{totalCount - enabledCount} In Development</span>
            </div>
          </div>
        </div>


        {/* Category Tabs */}
        <div className="mb-8">
          <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
            <TabsList className="grid w-full max-w-3xl mx-auto grid-cols-5 h-12 bg-muted/30 p-1">
              <TabsTrigger value="all" className="font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm">
                All Categories
              </TabsTrigger>
              <TabsTrigger value="business" className="font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm">
                Business
              </TabsTrigger>
              <TabsTrigger value="development" className="font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm">
                Development
              </TabsTrigger>
              <TabsTrigger value="media" className="font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm">
                Media
              </TabsTrigger>
              <TabsTrigger value="utilities" className="font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm">
                Utilities
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <p className="text-sm font-medium text-foreground">
                {isLoading ? "Searching..." : `${filteredTools.length} tools found`}
              </p>
              {activeCategory !== "all" && (
                <Badge variant="secondary" className="text-xs">
                  {toolCategories.find(cat => cat.name.toLowerCase() === activeCategory)?.name || activeCategory}
                </Badge>
              )}
            </div>
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSearch("")}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear search
              </Button>
            )}
          </div>
        </div>

        {/* Tools Grid/List */}
        {isLoading ? (
          <ToolCardSkeletonGrid count={8} />
        ) : (
          <div>
            {filteredTools.length === 0 ? (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                    <SearchIcon className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">No tools found</h3>
                  <p className="text-muted-foreground mb-6">
                    No tools match your current search criteria. Try adjusting your filters or search terms.
                  </p>
                  <Button
                    onClick={() => {
                      setSearchQuery("");
                      setActiveCategory("all");
                      setFilterBy("all");
                    }}
                    variant="outline"
                  >
                    Reset All Filters
                  </Button>
                </div>
              </div>
            ) : (
              <div className={
                viewMode === "grid"
                  ? "grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                  : "space-y-3"
              }>
                {filteredTools.map((tool) => (
                  <ToolCard
                    key={tool.id}
                    tool={tool}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
}
