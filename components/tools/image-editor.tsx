'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
    Crop,
    Download,
    Eraser,
    Palette,
    Plus,
    Minus,
    Redo,
    Save,
    Search,
    Settings,
    Trash2,
    Type,
    Undo,
    Upload,
    ZoomIn,
    ZoomOut,
    RotateCcw,
    RotateCw,
    FlipHorizontal,
    FlipVertical
} from "lucide-react";
import React, { useRef, useState, useEffect } from 'react';

// Main component for the image editor
export function ImageEditor() {
    const [image, setImage] = useState<string | null>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [history, setHistory] = useState<ImageData[]>([]);
    const [historyIndex, setHistoryIndex] = useState(-1);
    const [zoom, setZoom] = useState(1);
    const [isCropping, setIsCropping] = useState(false);
    const [cropRect, setCropRect] = useState({ x: 0, y: 0, width: 0, height: 0 });
    const [brightness, setBrightness] = useState(100);
    const [contrast, setContrast] = useState(100);
    const [saturation, setSaturation] = useState(100);
    const [text, setText] = useState('');
    const [textColor, setTextColor] = useState('#000000');
    const [fontSize, setFontSize] = useState(48);

    // Handle image upload
    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const reader = new FileReader();
            reader.onload = (e) => {
                setImage(e.target?.result as string);
            };
            reader.readAsDataURL(event.target.files[0]);
        }
    };

    // Draw image on canvas and apply adjustments
    useEffect(() => {
        if (image && canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                const img = new Image();
                img.src = image;
                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`;
                    ctx.drawImage(img, 0, 0);
                    saveHistory();
                };
            }
        }
    }, [image, brightness, contrast, saturation]);

    // Save canvas state for undo/redo
    const saveHistory = () => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const newHistory = history.slice(0, historyIndex + 1);
                newHistory.push(imageData);
                setHistory(newHistory);
                setHistoryIndex(newHistory.length - 1);
            }
        }
    };

    // Add text to image
    const handleAddText = () => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                ctx.font = `${fontSize}px Arial`;
                ctx.fillStyle = textColor;
                ctx.fillText(text, 50, 50); // Position can be improved
                saveHistory();
            }
        }
    };

    // Undo action
    const handleUndo = () => {
        if (historyIndex > 0) {
            const newIndex = historyIndex - 1;
            setHistoryIndex(newIndex);
            restoreHistory(newIndex);
        }
    };

    // Redo action
    const handleRedo = () => {
        if (historyIndex < history.length - 1) {
            const newIndex = historyIndex + 1;
            setHistoryIndex(newIndex);
            restoreHistory(newIndex);
        }
    };

    // Restore canvas state from history
    const restoreHistory = (index: number) => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                const imageData = history[index];
                canvas.width = imageData.width;
                canvas.height = imageData.height;
                ctx.putImageData(imageData, 0, 0);
            }
        }
    };

    // Apply filter to image
    const applyFilter = (filter: string) => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;
                for (let i = 0; i < data.length; i += 4) {
                    switch (filter) {
                        case 'grayscale':
                            const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
                            data[i] = avg;
                            data[i + 1] = avg;
                            data[i + 2] = avg;
                            break;
                        case 'sepia':
                            const r = data[i];
                            const g = data[i + 1];
                            const b = data[i + 2];
                            data[i] = r * 0.393 + g * 0.769 + b * 0.189;
                            data[i + 1] = r * 0.349 + g * 0.686 + b * 0.168;
                            data[i + 2] = r * 0.272 + g * 0.534 + b * 0.131;
                            break;
                        case 'invert':
                            data[i] = 255 - data[i];
                            data[i + 1] = 255 - data[i + 1];
                            data[i + 2] = 255 - data[i + 2];
                            break;
                        default:
                            break;
                    }
                }
                ctx.putImageData(imageData, 0, 0);
                saveHistory();
            }
        }
    };

    // Handle image download
    const handleDownload = () => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const link = document.createElement('a');
            link.download = 'edited-image.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    };

    // Handle zoom
    const handleZoom = (newZoom: number) => {
      setZoom(newZoom);
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        canvas.style.transform = `scale(${newZoom})`;
      }
    };

    const [isDragging, setIsDragging] = useState(false);
    const [startPos, setStartPos] = useState({ x: 0, y: 0 });

    const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
        if (!isCropping) return;
        const rect = canvasRef.current!.getBoundingClientRect();
        setStartPos({
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
        });
        setIsDragging(true);
    };

    const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
        if (!isCropping || !isDragging) return;
        const rect = canvasRef.current!.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;
        const width = currentX - startPos.x;
        const height = currentY - startPos.y;
        setCropRect({
            x: startPos.x,
            y: startPos.y,
            width: width,
            height: height,
        });
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    // Handle crop
    const handleCrop = () => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                const { x, y, width, height } = cropRect;
                const croppedImageData = ctx.getImageData(x, y, width, height);
                canvas.width = width;
                canvas.height = height;
                ctx.putImageData(croppedImageData, 0, 0);
                saveHistory();
                setIsCropping(false);
            }
        }
    };

    return (
        <div className="flex flex-col h-screen bg-gray-100 dark:bg-gray-900">
            {/* Header */}
            <header className="flex items-center justify-between px-6 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-4">
                    <Palette className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                    <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Image Editor</h1>
                </div>
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" onClick={handleUndo} disabled={historyIndex <= 0}>
                        <Undo className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={handleRedo} disabled={historyIndex >= history.length - 1}>
                        <Redo className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleDownload} disabled={!image}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                    </Button>
                </div>
            </header>

            <div className="flex flex-1 overflow-hidden">
                {/* Sidebar */}
                <aside className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto">
                    <Tabs defaultValue="upload" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="upload">
                                <Upload className="h-4 w-4 mr-2" />
                                Upload
                            </TabsTrigger>
                            <TabsTrigger value="edit">
                                <Settings className="h-4 w-4 mr-2" />
                                Edit
                            </TabsTrigger>
                            <TabsTrigger value="text">
                                <Type className="h-4 w-4 mr-2" />
                                Text
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="upload" className="mt-6">
                            <Card>
                                <CardContent className="p-6">
                                    <Label htmlFor="image-upload" className="cursor-pointer flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <Upload className="h-8 w-8 text-gray-500" />
                                        <p className="mt-2 text-sm text-gray-500">Click to upload an image</p>
                                        <Input id="image-upload" type="file" accept="image/*" className="hidden" onChange={handleImageUpload} />
                                    </Label>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="edit" className="mt-6 space-y-6">
                             <Card>
                                <CardContent className="p-6 space-y-4">
                                     <h3 className="text-sm font-medium">Filters</h3>
                                     <div className="grid grid-cols-3 gap-2">
                                         <Button variant="outline" size="sm" onClick={() => applyFilter('grayscale')}>Grayscale</Button>
                                         <Button variant="outline" size="sm" onClick={() => applyFilter('sepia')}>Sepia</Button>
                                         <Button variant="outline" size="sm" onClick={() => applyFilter('invert')}>Invert</Button>
                                     </div>
                                 </CardContent>
                             </Card>
                             <Card>
                                <CardContent className="p-6 space-y-4">
                                     <h3 className="text-sm font-medium">Adjustments</h3>
                                     <div className="space-y-2">
                                        <Label>Brightness</Label>
                                        <Slider value={[brightness]} onValueChange={(value) => setBrightness(value[0])} max={200} />
                                     </div>
                                     <div className="space-y-2">
                                        <Label>Contrast</Label>
                                        <Slider value={[contrast]} onValueChange={(value) => setContrast(value[0])} max={200} />
                                     </div>
                                     <div className="space-y-2">
                                        <Label>Saturation</Label>
                                        <Slider value={[saturation]} onValueChange={(value) => setSaturation(value[0])} max={200} />
                                     </div>
                                 </CardContent>
                             </Card>
                             <Card>
                                <CardContent className="p-6 space-y-4">
                                     <h3 className="text-sm font-medium">Crop & Resize</h3>
                                    <Button variant="outline" size="sm" onClick={() => setIsCropping(!isCropping)}>
                                        <Crop className="h-4 w-4 mr-2" />
                                        {isCropping ? 'Cancel Crop' : 'Crop Image'}
                                    </Button>
                                    {isCropping && (
                                        <Button variant="default" size="sm" onClick={handleCrop}>
                                            Apply Crop
                                        </Button>
                                    )}
                                 </CardContent>
                             </Card>
                        </TabsContent>

                        <TabsContent value="text" className="mt-6">
                            <Card>
                                <CardContent className="p-6 space-y-4">
                                    <h3 className="text-sm font-medium">Add Text</h3>
                                    <Input placeholder="Enter text" value={text} onChange={(e) => setText(e.target.value)} />
                                    <div className="flex items-center gap-4">
                                        <Label>Color</Label>
                                        <Input type="color" value={textColor} onChange={(e) => setTextColor(e.target.value)} />
                                    </div>
                                    <div className="flex items-center gap-4">
                                        <Label>Font Size</Label>
                                        <Input type="number" value={fontSize} onChange={(e) => setFontSize(parseInt(e.target.value))} />
                                    </div>
                                    <Button onClick={handleAddText}>Add Text</Button>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </aside>

                {/* Main Content */}
                <main className="flex-1 flex items-center justify-center p-6 bg-gray-200 dark:bg-gray-900/50 overflow-auto">
                    <div className="relative">
                        <canvas
                            ref={canvasRef}
                            className="max-w-full max-h-full shadow-lg"
                            style={{ transform: `scale(${zoom})`, cursor: isCropping ? 'crosshair' : 'default' }}
                            onMouseDown={handleMouseDown}
                            onMouseMove={handleMouseMove}
                            onMouseUp={handleMouseUp}
                        />
                        {isCropping && (
                            <div
                                className="absolute border-2 border-dashed border-blue-500"
                                style={{
                                    left: cropRect.x,
                                    top: cropRect.y,
                                    width: cropRect.width,
                                    height: cropRect.height,
                                }}
                            />
                        )}
                    </div>
                </main>
            </div>
        </div>
    );
}
