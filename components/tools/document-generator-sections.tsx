'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
    closestCenter,
    DndContext,
    DragEndEvent,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
    Calculator,
    Download,
    Eye,
    GripVertical,
    Plus,
    Settings,
    Trash2
} from "lucide-react";
import dynamic from "next/dynamic";
import { DocumentPreview } from "./document-preview";
import { PDFTemplate } from "./pdf-template";
import { TemplateSelector, TemplateType } from "./template-selector";

const PDFDownloadLink = dynamic(
  () => import('@react-pdf/renderer').then(mod => mod.PDFDownloadLink),
  { ssr: false }
);

interface DocumentItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

interface DocumentData {
  number: string;
  date: string;
  dueDate?: string;
  validUntil?: string;
  from: {
    name: string;
    address: string;
    email: string;
    phone: string;
    logo?: string;
  };
  to: {
    name: string;
    address: string;
    email: string;
    phone: string;
  };
  notes?: string;
  terms?: string;
}

interface ItemsSectionProps {
  items: DocumentItem[];
  setItems: (items: DocumentItem[]) => void;
  currency: string;
  taxRate: number;
  discountRate: number;
  updateCompletedSections: () => void;
}

interface TemplateSectionProps {
  type: "invoice" | "quote";
  selectedTemplate: TemplateType;
  setSelectedTemplate: (template: TemplateType) => void;
  documentData: DocumentData;
  setDocumentData: (data: DocumentData) => void;
}

interface GenerateButtonProps {
  type: "invoice" | "quote";
  documentData: DocumentData;
  items: DocumentItem[];
  selectedTemplate: TemplateType;
  currency: string;
  calculateSubtotal: () => number;
  calculateTax: () => { rate: number; amount: number };
  calculateDiscount: () => { rate: number; amount: number };
  calculateTotal: () => number;
  isFormValid: () => boolean;
  setShowPreview: (show: boolean) => void;
  primaryColor: string;
}

function SortableItem({ item, index, updateItem, removeItem }: {
  item: DocumentItem;
  index: number;
  updateItem: (index: number, field: keyof DocumentItem, value: any) => void;
  removeItem: (index: number) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "grid gap-4 p-4 border rounded-lg bg-card transition-all duration-200",
        isDragging ? 'shadow-lg ring-2 ring-blue-500/20' : 'hover:shadow-md'
      )}
    >
      <div className="flex items-center gap-3">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-2 hover:bg-muted rounded-md transition-colors"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 flex-1">
          <div className="md:col-span-2 space-y-2">
            <Label className="text-sm font-medium">Description</Label>
            <Input
              value={item.description}
              onChange={(e) => updateItem(index, "description", e.target.value)}
              placeholder="Enter item description..."
              className="h-10"
            />
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Quantity</Label>
            <Input
              type="number"
              min="1"
              value={item.quantity}
              onChange={(e) => updateItem(index, "quantity", parseInt(e.target.value) || 1)}
              className="h-10"
            />
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rate</Label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={item.rate}
              onChange={(e) => updateItem(index, "rate", parseFloat(e.target.value) || 0)}
              className="h-10"
            />
          </div>
        </div>
        <div className="flex flex-col items-end gap-2">
          <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            ${item.amount.toFixed(2)}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeItem(index)}
            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export function ItemsSection({ 
  items, 
  setItems, 
  currency, 
  taxRate, 
  discountRate, 
  updateCompletedSections 
}: ItemsSectionProps) {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const addItem = () => {
    const newId = Date.now().toString();
    setItems([...items, { id: newId, description: "", quantity: 1, rate: 0, amount: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
    updateCompletedSections();
  };

  const updateItem = (index: number, field: keyof DocumentItem, value: any) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };

    // Calculate amount when quantity or rate changes
    if (field === "quantity" || field === "rate") {
      newItems[index].amount = newItems[index].quantity * newItems[index].rate;
    }

    setItems(newItems);
    updateCompletedSections();
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over?.id);

      setItems(arrayMove(items, oldIndex, newIndex));
    }
  };

  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.amount, 0);
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal();
    const amount = (subtotal * taxRate) / 100;
    return { rate: taxRate, amount };
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    const amount = (subtotal * discountRate) / 100;
    return { rate: discountRate, amount };
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax();
    const discount = calculateDiscount();
    return subtotal + tax.amount - discount.amount;
  };

  const currencySymbol = currency === "USD" ? "$" : currency === "EUR" ? "€" : currency === "GBP" ? "£" : "$";

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Items & Pricing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={items.map(item => item.id)} strategy={verticalListSortingStrategy}>
            <div className="space-y-4">
              {items.map((item, index) => (
                <SortableItem
                  key={item.id}
                  item={item}
                  index={index}
                  updateItem={updateItem}
                  removeItem={removeItem}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        <Button onClick={addItem} variant="outline" className="w-full">
          <Plus className="mr-2 h-4 w-4" />
          Add Item
        </Button>

        {/* Totals */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 space-y-3">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>{currencySymbol}{calculateSubtotal().toFixed(2)}</span>
          </div>
          {discountRate > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Discount ({discountRate}%):</span>
              <span>-{currencySymbol}{calculateDiscount().amount.toFixed(2)}</span>
            </div>
          )}
          {taxRate > 0 && (
            <div className="flex justify-between text-sm">
              <span>Tax ({taxRate}%):</span>
              <span>{currencySymbol}{calculateTax().amount.toFixed(2)}</span>
            </div>
          )}
          <Separator />
          <div className="flex justify-between font-bold text-lg">
            <span>Total:</span>
            <span>{currencySymbol}{calculateTotal().toFixed(2)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function TemplateSection({ 
  type, 
  selectedTemplate, 
  setSelectedTemplate, 
  documentData, 
  setDocumentData 
}: TemplateSectionProps) {
  return (
    <div className="space-y-6">
      <TemplateSelector
        selectedTemplate={selectedTemplate}
        onTemplateChange={setSelectedTemplate}
      />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Additional Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {type === 'quote' && (
            <div className="space-y-2">
              <Label htmlFor="terms">Terms & Conditions</Label>
              <Textarea
                id="terms"
                value={documentData.terms || ''}
                onChange={(e) =>
                  setDocumentData({ ...documentData, terms: e.target.value })
                }
                placeholder="Payment terms, project scope, etc."
                rows={4}
              />
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={documentData.notes || ''}
              onChange={(e) =>
                setDocumentData({ ...documentData, notes: e.target.value })
              }
              placeholder="Additional information, special instructions, etc."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function GenerateButton({
  type,
  documentData,
  items,
  selectedTemplate,
  currency,
  calculateSubtotal,
  calculateTax,
  calculateDiscount,
  calculateTotal,
  isFormValid,
  setShowPreview,
  primaryColor
}: GenerateButtonProps) {
  return (
    <div className="fixed bottom-0 left-64 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-6">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row gap-4 justify-center items-center">
        {isFormValid() ? (
          <PDFDownloadLink
            document={
              <PDFTemplate
                type={type}
                template={selectedTemplate}
                number={documentData.number}
                date={documentData.date}
                dueDate={documentData.dueDate}
                validUntil={documentData.validUntil}
                from={documentData.from}
                to={documentData.to}
                items={items}
                subtotal={calculateSubtotal()}
                tax={calculateTax()}
                discount={calculateDiscount()}
                total={calculateTotal()}
                currency={currency}
                terms={documentData.terms}
                notes={documentData.notes}
              />
            }
            fileName={`${type}-${documentData.number}.pdf`}
          >
            {({ loading }) => (
              <Button
                size="lg"
                disabled={loading}
                className={cn(
                  "min-w-[250px] h-12 text-white shadow-lg hover:shadow-xl transition-all duration-200",
                  primaryColor
                )}
              >
                <Download className="mr-2 h-5 w-5" />
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating PDF...
                  </>
                ) : (
                  `Generate Professional ${type === 'invoice' ? 'Invoice' : 'Quote'}`
                )}
              </Button>
            )}
          </PDFDownloadLink>
        ) : (
          <Button size="lg" disabled className="min-w-[250px] h-12">
            <Download className="mr-2 h-5 w-5" />
            Complete Required Fields
          </Button>
        )}

        <Sheet onOpenChange={setShowPreview}>
          <SheetTrigger asChild>
            <Button variant="outline" size="lg" className="cursor-pointer">
              <Eye className="mr-2 h-4 w-4" />
              Preview First
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="sm:max-w-4xl overflow-y-auto p-8">
            <DocumentPreview
              type={type}
              documentData={documentData}
              items={items}
              totals={{
                subtotal: calculateSubtotal(),
                tax: calculateTax(),
                discount: calculateDiscount(),
                total: calculateTotal()
              }}
              currency={currency}
              template={selectedTemplate}
            />
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}
